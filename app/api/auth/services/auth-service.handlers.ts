import { http, HttpResponse } from 'msw'
import { buildTokenResponseSchema } from '~/schemas/auth/token-response.schema.builder'
import { AUTH_SERVICE_API_ROUTES } from './auth-service.routes'

export const authServiceHandlers = [
  http.post(AUTH_SERVICE_API_ROUTES.TOKEN, () => {
    return HttpResponse.json(buildTokenResponseSchema(), { status: 200 })
  }),

  http.post(AUTH_SERVICE_API_ROUTES.LOGOUT, () => {
    return HttpResponse.json({}, { status: 200 })
  }),
]
