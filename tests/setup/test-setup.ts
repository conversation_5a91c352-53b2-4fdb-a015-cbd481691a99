import { setup } from '@nuxt/test-utils'
import { setupServer } from 'msw/node'
import { afterAll, afterEach, beforeAll } from 'vitest'
import { authServiceHandlers } from '~/api/auth/services/auth-service.handlers'

export const mockServer = setupServer(
  ...authServiceHandlers,
)

beforeAll(async () => {
  mockServer.listen()
  await setup({ testDir: __dirname })
})

afterEach(() => mockServer.resetHandlers())
afterAll(() => mockServer.close())
