import { mockNuxtImport } from '@nuxt/test-utils/runtime'
import { createTestingPinia } from '@pinia/testing'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { AuthService } from '~/api/auth/services/auth.service'
import { useAuth } from '~/composables/auth/useAuth'
import { buildTokenResponseSchema } from '~/schemas/auth/token-response.schema.builder'

interface RouteMockQuery {
  code: string | undefined
}

const { mockUseRoute } = vi.hoisted(() => ({
  mockUseRoute: vi.fn(() => ({
    query: { code: 'test_code' } as RouteMockQuery,
  })),
}))
mockNuxtImport('useRoute', () => mockUseRoute)

export function setMockRoute(query: RouteMockQuery) {
  mockUseRoute.mockReturnValue({ query })
}

// Mock pkce-challenge module
vi.mock('pkce-challenge', () => ({
  default: vi.fn(async () => ({
    code_verifier: 'test_verifier',
    code_challenge: 'test_challenge',
  })),
}))

vi.mock('~/api/auth/services/auth.service', () => ({
  AuthService: { getToken: vi.fn() },
}))

vi.stubGlobal('window', Object.create(window, {
  location: {
    value: { href: '', assign: vi.fn((url) => { window.location.href = url }) },
    writable: true,
  },
}))

function useTestAuth(overrides: Partial<Parameters<typeof useAuth>[0]> = {}) {
  return useAuth({
    getRedirectUri: () => 'https://test.app/callback',
    getClientId: () => 'test-client-id',
    getApiBaseUrl: () => 'https://test.app/api',
    onTokenReceived: vi.fn(),
    fetchToken: (code, verifier) =>
      AuthService.getToken(code, verifier),
    ...overrides,
  })
}

describe('useAuth - Login Success', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    sessionStorage.clear()

    setMockRoute({ code: 'test_code' })

    createTestingPinia({
      stubActions: true,
      createSpy: vi.fn,
    })
  })

  it('calls window.location.href with correct login URL and stores verifier', async () => {
    const { login, generateRedirectUrl } = useTestAuth()
    await login()

    const expected = generateRedirectUrl('test_challenge')

    expect(window.location.href).toBe(expected)
    expect(sessionStorage.getItem('pkce_verifier')).toBe('test_verifier')
  })

  it('calls AuthService.getToken with code and verifier and sets auth on successful login', async () => {
    sessionStorage.setItem('pkce_verifier', 'test_verifier')

    const setAuthSpy = vi.fn()
    const mockToken = buildTokenResponseSchema()

    const mockedAuthService = vi.mocked(AuthService, true)
    mockedAuthService.getToken.mockResolvedValue(mockToken)

    const { handleRedirectCallback } = useTestAuth({
      onTokenReceived: setAuthSpy,
    })

    await handleRedirectCallback()

    expect(AuthService.getToken).toHaveBeenCalledWith('test_code', 'test_verifier')
    expect(setAuthSpy).toHaveBeenCalledWith(mockToken)
  })

  it('generates correct OAuth2 authorize URL with PKCE parameters', () => {
    const { generateRedirectUrl } = useTestAuth()

    const result = generateRedirectUrl('test_challenge')
    const url = new URL(result)

    expect(url.hostname).toBe('test.app')
    expect(url.pathname).toBe('/api/oauth/authorize')
    expect(url.searchParams.get('response_type')).toBe('code')
    expect(url.searchParams.get('client_id')).toBe('test-client-id')
    expect(url.searchParams.get('redirect_uri')).toBe('https://test.app/callback')
    expect(url.searchParams.get('code_challenge')).toBe('test_challenge')
    expect(url.searchParams.get('code_challenge_method')).toBe('S256')
    expect(url.searchParams.get('prompt')).toBe('login')
  })

  it('throws if no code is found in route query', async () => {
    setMockRoute({ code: undefined })

    const { handleRedirectCallback } = useTestAuth()

    await expect(() => handleRedirectCallback()).rejects.toThrow('Missing authorization code')
  })

  it('throws if no verifier is found in session storage', async () => {
    const { handleRedirectCallback } = useTestAuth()

    await expect(() => handleRedirectCallback()).rejects.toThrow('Missing PKCE verifier')
  })
})
