# Auth Tests Documentation

## Overview

This document describes the authentication tests implemented in the project, focusing on the **login success** flow.

## Test Structure

### Files Created

- `tests/auth/use-auth-login-success.spec.ts` - Main test file for login success scenarios
- `app/schemas/auth/token-response.schema.builder.ts` - Builder for creating test token responses
- `app/api/auth/services/auth-service.handlers.ts` - MSW handlers for auth API endpoints

## Login Success Tests

### Test Coverage

The login success test suite covers the following scenarios:

1. **Successful Login Flow**
   - Calls `window.location.href` with correct OAuth2 URL
   - Stores PKCE verifier in sessionStorage
   - Generates proper OAuth2 authorize URL with PKCE parameters

2. **Successful Token Exchange**
   - Calls `AuthService.getToken` with authorization code and verifier
   - Calls `onTokenReceived` callback with token response
   - Validates proper handling of OAuth2 callback

3. **URL Generation Validation**
   - Verifies correct OAuth2 authorize URL structure
   - Validates PKCE parameters (code_challenge, code_challenge_method)
   - Confirms proper redirect URI and client ID

4. **Error Handling**
   - Throws error when authorization code is missing
   - Throws error when PKCE verifier is missing from sessionStorage

### Technology Stack

- **Testing Framework**: Vitest
- **Mocking**: MSW (Mock Service Worker) for API calls
- **State Management**: Pinia with `@pinia/testing`
- **Nuxt Testing**: `@nuxt/test-utils` for Nuxt-specific mocks

### Authentication Flow

The project uses **OAuth2 with PKCE** authentication:

1. User initiates login
2. PKCE challenge/verifier pair is generated using `pkce-challenge` library
3. User is redirected to OAuth2 authorization endpoint
4. After authorization, callback contains authorization code
5. Code is exchanged for access/refresh tokens using PKCE verifier
6. Tokens are stored in secure cookies via `useUserStore`

### Key Components Tested

- `useAuth` composable - Core authentication logic
- `AuthService.getToken` - Token exchange API call
- PKCE flow - Challenge generation and verification
- Session management - Token storage in cookies

### Test Configuration

The tests use:
- **MSW** for mocking HTTP requests
- **vi.mock** for mocking external dependencies
- **createTestingPinia** for isolated state management
- **mockNuxtImport** for Nuxt-specific mocks

### Running Tests

```bash
# Run all auth tests
npm test tests/auth/

# Run specific login success tests
npm test tests/auth/use-auth-login-success.spec.ts

# Run tests in watch mode
npm run test:watch
```

## Differences from Reference Project

The current implementation differs from the reference project:

1. **PKCE Implementation**: Uses `pkce-challenge` library instead of custom `usePKCE`
2. **Token Storage**: Uses secure cookies instead of localStorage
3. **State Management**: Uses Pinia instead of manual state management
4. **Test Setup**: Integrated with existing MSW server configuration

## Future Improvements

Planned additions (not implemented yet):
- Invalid credentials tests
- Session restore tests
- Integration tests with actual OAuth2 provider
- E2E authentication flow tests 